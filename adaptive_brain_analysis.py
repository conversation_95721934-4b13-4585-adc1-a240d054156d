#!/usr/bin/env python3
"""
自适应脑功能连接数据分析与可视化
根据可用数据自动选择分析策略
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import os
import glob
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveBrainAnalyzer:
    def __init__(self, data_dir='data', node_features_dir='node_features'):
        """
        初始化分析器
        
        Parameters:
        -----------
        data_dir : str
            FC矩阵数据目录
        node_features_dir : str
            时间序列数据目录
        """
        self.data_dir = data_dir
        self.node_features_dir = node_features_dir
        self.hc_fc_data = []
        self.mci_fc_data = []
        self.hc_ts_data = []
        self.mci_ts_data = []
        self.hc_features = []
        self.mci_features = []
        self.analysis_mode = None
        
    def load_data(self):
        """加载HC和MCI组的数据"""
        print("正在加载数据...")
        
        # 加载HC组FC矩阵
        hc_fc_files = glob.glob(os.path.join(self.data_dir, 'HC', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(hc_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.hc_fc_data.append(fc_matrix)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        # 加载MCI组FC矩阵
        mci_fc_files = glob.glob(os.path.join(self.data_dir, 'MCI', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(mci_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.mci_fc_data.append(fc_matrix)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        # 加载HC组时间序列
        hc_ts_files = glob.glob(os.path.join(self.node_features_dir, 'HC', 'ROISignals_*.txt'))
        for file in sorted(hc_ts_files):
            try:
                ts_data = np.loadtxt(file)
                if ts_data.shape == (131, 116):
                    self.hc_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        # 加载MCI组时间序列
        mci_ts_files = glob.glob(os.path.join(self.node_features_dir, 'MCI', 'ROISignals_*.txt'))
        for file in sorted(mci_ts_files):
            try:
                ts_data = np.loadtxt(file)
                if ts_data.shape == (131, 116):
                    self.mci_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        print(f"成功加载 HC组: {len(self.hc_fc_data)} 个FC矩阵, {len(self.hc_ts_data)} 个时间序列")
        print(f"成功加载 MCI组: {len(self.mci_fc_data)} 个FC矩阵, {len(self.mci_ts_data)} 个时间序列")
        
        # 确定分析模式
        self._determine_analysis_mode()
    
    def _determine_analysis_mode(self):
        """根据可用数据确定分析模式"""
        has_hc_fc = len(self.hc_fc_data) > 0
        has_mci_fc = len(self.mci_fc_data) > 0
        has_hc_ts = len(self.hc_ts_data) > 0
        has_mci_ts = len(self.mci_ts_data) > 0
        
        if has_hc_fc and has_mci_fc and has_hc_ts and has_mci_ts:
            self.analysis_mode = "combined"
            print("分析模式: 结合FC矩阵和时间序列特征")
        elif has_hc_fc and has_mci_fc:
            self.analysis_mode = "fc_only"
            print("分析模式: 仅使用FC矩阵特征")
        elif has_hc_ts and has_mci_ts:
            self.analysis_mode = "ts_only"
            print("分析模式: 仅使用时间序列特征")
        else:
            self.analysis_mode = "insufficient"
            print("警告: 数据不足，无法进行分析")
    
    def calculate_temporal_features(self, ts_data):
        """计算时间序列的时域统计特征"""
        features = []
        
        # 对每个脑区计算统计特征
        for region in range(ts_data.shape[1]):
            region_ts = ts_data[:, region]
            
            # 基本统计特征
            mean_val = np.mean(region_ts)
            std_val = np.std(region_ts)
            var_val = np.var(region_ts)
            min_val = np.min(region_ts)
            max_val = np.max(region_ts)
            range_val = max_val - min_val
            
            # 高阶统计特征
            skewness = stats.skew(region_ts)
            kurtosis = stats.kurtosis(region_ts)
            
            # 百分位数
            q25 = np.percentile(region_ts, 25)
            q50 = np.percentile(region_ts, 50)
            q75 = np.percentile(region_ts, 75)
            iqr = q75 - q25
            
            # 能量相关特征
            energy = np.sum(region_ts ** 2)
            rms = np.sqrt(np.mean(region_ts ** 2))
            
            # 变异系数
            cv = std_val / abs(mean_val) if mean_val != 0 else 0
            
            features.extend([
                mean_val, std_val, var_val, min_val, max_val, range_val,
                skewness, kurtosis, q25, q50, q75, iqr, energy, rms, cv
            ])
        
        return np.array(features)
    
    def extract_fc_features(self, fc_matrix):
        """提取FC矩阵特征"""
        # 处理无穷大和NaN值
        fc_matrix_clean = np.copy(fc_matrix)
        np.fill_diagonal(fc_matrix_clean, 0)
        
        # 处理无穷大值
        inf_mask = np.isinf(fc_matrix_clean)
        if np.any(inf_mask):
            finite_values = fc_matrix_clean[np.isfinite(fc_matrix_clean)]
            if len(finite_values) > 0:
                max_finite = np.max(finite_values)
                min_finite = np.min(finite_values)
                fc_matrix_clean[fc_matrix_clean == np.inf] = max_finite
                fc_matrix_clean[fc_matrix_clean == -np.inf] = min_finite
            else:
                fc_matrix_clean[inf_mask] = 0
        
        # 处理NaN值
        nan_mask = np.isnan(fc_matrix_clean)
        if np.any(nan_mask):
            fc_matrix_clean[nan_mask] = 0
        
        # 提取上三角矩阵（去除对角线）
        upper_tri_indices = np.triu_indices(fc_matrix_clean.shape[0], k=1)
        fc_features = fc_matrix_clean[upper_tri_indices]
        
        # 计算网络级别的统计特征
        network_features = []
        
        # 全局统计特征
        if len(fc_features) > 0:
            network_features.extend([
                np.mean(fc_features),
                np.std(fc_features),
                np.var(fc_features),
                np.min(fc_features),
                np.max(fc_features),
                np.median(fc_features),
                stats.skew(fc_features) if len(fc_features) > 1 else 0,
                stats.kurtosis(fc_features) if len(fc_features) > 1 else 0
            ])
        else:
            network_features.extend([0] * 8)
        
        # 节点度中心性
        node_strengths = np.sum(np.abs(fc_matrix_clean), axis=1)
        if len(node_strengths) > 0:
            network_features.extend([
                np.mean(node_strengths),
                np.std(node_strengths),
                np.max(node_strengths),
                np.min(node_strengths)
            ])
        else:
            network_features.extend([0] * 4)
        
        # 根据分析模式返回不同的特征
        if self.analysis_mode == "fc_only":
            # 返回原始FC特征和网络特征
            return np.concatenate([fc_features, network_features])
        else:
            # 只返回网络特征以减少维度
            return np.array(network_features)
    
    def process_features(self):
        """处理所有被试的特征"""
        print("正在计算特征...")
        
        if self.analysis_mode == "insufficient":
            print("数据不足，无法进行特征提取")
            return
        
        if self.analysis_mode == "combined":
            # 结合FC和时间序列特征
            min_samples = min(len(self.hc_fc_data), len(self.mci_fc_data), 
                            len(self.hc_ts_data), len(self.mci_ts_data))
            print(f"使用前 {min_samples} 个样本进行结合分析")
            
            # 处理HC组
            for i in range(min_samples):
                fc_features = self.extract_fc_features(self.hc_fc_data[i])
                temporal_features = self.calculate_temporal_features(self.hc_ts_data[i])
                combined_features = np.concatenate([fc_features, temporal_features])
                combined_features = np.nan_to_num(combined_features, nan=0.0, posinf=0.0, neginf=0.0)
                self.hc_features.append(combined_features)
            
            # 处理MCI组
            for i in range(min_samples):
                fc_features = self.extract_fc_features(self.mci_fc_data[i])
                temporal_features = self.calculate_temporal_features(self.mci_ts_data[i])
                combined_features = np.concatenate([fc_features, temporal_features])
                combined_features = np.nan_to_num(combined_features, nan=0.0, posinf=0.0, neginf=0.0)
                self.mci_features.append(combined_features)
                
        elif self.analysis_mode == "fc_only":
            # 仅使用FC特征
            min_samples = min(len(self.hc_fc_data), len(self.mci_fc_data))
            print(f"使用 {min_samples} 个样本进行FC分析")
            
            # 处理HC组
            for fc_data in self.hc_fc_data:
                features = self.extract_fc_features(fc_data)
                features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
                self.hc_features.append(features)
            
            # 处理MCI组
            for fc_data in self.mci_fc_data:
                features = self.extract_fc_features(fc_data)
                features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
                self.mci_features.append(features)
                
        elif self.analysis_mode == "ts_only":
            # 仅使用时间序列特征
            min_samples = min(len(self.hc_ts_data), len(self.mci_ts_data))
            print(f"使用 {min_samples} 个样本进行时间序列分析")
            
            # 处理HC组
            for ts_data in self.hc_ts_data:
                features = self.calculate_temporal_features(ts_data)
                features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
                self.hc_features.append(features)
            
            # 处理MCI组
            for ts_data in self.mci_ts_data:
                features = self.calculate_temporal_features(ts_data)
                features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
                self.mci_features.append(features)
        
        print(f"HC组特征维度: {len(self.hc_features)} × {len(self.hc_features[0]) if self.hc_features else 0}")
        print(f"MCI组特征维度: {len(self.mci_features)} × {len(self.mci_features[0]) if self.mci_features else 0}")
    
    def perform_tsne_analysis(self, perplexity=30, n_components=2, random_state=42):
        """执行t-SNE降维分析"""
        print("正在进行t-SNE降维分析...")
        
        if len(self.hc_features) == 0 or len(self.mci_features) == 0:
            print("错误: 没有足够的数据进行分析")
            return None, None
        
        # 合并所有特征
        all_features = np.array(self.hc_features + self.mci_features)
        labels = ['HC'] * len(self.hc_features) + ['MCI'] * len(self.mci_features)
        
        print(f"总样本数: {len(all_features)}")
        print(f"特征维度: {all_features.shape[1]}")
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(all_features)
        
        # 先用PCA降维以提高t-SNE效率
        if features_scaled.shape[1] > 50:
            print("使用PCA预处理...")
            pca = PCA(n_components=50, random_state=random_state)
            features_pca = pca.fit_transform(features_scaled)
            print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
        else:
            features_pca = features_scaled
        
        # 调整困惑度参数
        n_samples = len(all_features)
        perplexity = min(perplexity, (n_samples - 1) // 3)
        print(f"使用困惑度: {perplexity}")
        
        # t-SNE降维
        tsne = TSNE(n_components=n_components, perplexity=perplexity, 
                   random_state=random_state, n_iter=1000, verbose=1)
        features_tsne = tsne.fit_transform(features_pca)
        
        return features_tsne, labels
