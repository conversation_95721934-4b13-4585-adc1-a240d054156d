#!/usr/bin/env python3
"""
高级脑功能连接数据分析
包括特征重要性分析、分类性能评估和统计检验
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedBrainAnalyzer:
    def __init__(self):
        """初始化高级分析器"""
        self.fc_data = None
        self.ts_data = None
        self.combined_data = None
        self.labels = None
        
    def load_analysis_results(self):
        """加载之前的分析结果"""
        print("正在加载分析结果...")
        
        # 加载FC分析结果
        try:
            self.fc_data = pd.read_csv('results/fc_tsne_results.csv')
            print(f"FC数据加载成功: {len(self.fc_data)} 个样本")
        except:
            print("FC数据加载失败")
            
        # 加载时间序列分析结果
        try:
            self.ts_data = pd.read_csv('results_timeseries/timeseries_tsne_results.csv')
            print(f"时间序列数据加载成功: {len(self.ts_data)} 个样本")
        except:
            print("时间序列数据加载失败")
    
    def statistical_analysis(self):
        """进行统计分析"""
        print("\n=== 统计分析 ===")
        
        results = {}
        
        if self.fc_data is not None:
            print("\n1. FC数据统计分析:")
            hc_fc = self.fc_data[self.fc_data['Group'] == 'HC']
            mci_fc = self.fc_data[self.fc_data['Group'] == 'MCI']
            
            # t检验
            t_stat_1, p_val_1 = stats.ttest_ind(hc_fc['tSNE1'], mci_fc['tSNE1'])
            t_stat_2, p_val_2 = stats.ttest_ind(hc_fc['tSNE2'], mci_fc['tSNE2'])
            
            print(f"  tSNE1维度: t={t_stat_1:.3f}, p={p_val_1:.6f}")
            print(f"  tSNE2维度: t={t_stat_2:.3f}, p={p_val_2:.6f}")
            
            # Mann-Whitney U检验
            u_stat_1, u_p_1 = stats.mannwhitneyu(hc_fc['tSNE1'], mci_fc['tSNE1'])
            u_stat_2, u_p_2 = stats.mannwhitneyu(hc_fc['tSNE2'], mci_fc['tSNE2'])
            
            print(f"  Mann-Whitney U检验:")
            print(f"    tSNE1: U={u_stat_1:.3f}, p={u_p_1:.6f}")
            print(f"    tSNE2: U={u_stat_2:.3f}, p={u_p_2:.6f}")
            
            results['fc'] = {
                'ttest_tsne1': (t_stat_1, p_val_1),
                'ttest_tsne2': (t_stat_2, p_val_2),
                'mannwhitney_tsne1': (u_stat_1, u_p_1),
                'mannwhitney_tsne2': (u_stat_2, u_p_2)
            }
        
        if self.ts_data is not None:
            print("\n2. 时间序列数据统计分析:")
            hc_ts = self.ts_data[self.ts_data['Group'] == 'HC']
            mci_ts = self.ts_data[self.ts_data['Group'] == 'MCI']
            
            # t检验
            t_stat_1, p_val_1 = stats.ttest_ind(hc_ts['tSNE1'], mci_ts['tSNE1'])
            t_stat_2, p_val_2 = stats.ttest_ind(hc_ts['tSNE2'], mci_ts['tSNE2'])
            
            print(f"  tSNE1维度: t={t_stat_1:.3f}, p={p_val_1:.6f}")
            print(f"  tSNE2维度: t={t_stat_2:.3f}, p={p_val_2:.6f}")
            
            # Mann-Whitney U检验
            u_stat_1, u_p_1 = stats.mannwhitneyu(hc_ts['tSNE1'], mci_ts['tSNE1'])
            u_stat_2, u_p_2 = stats.mannwhitneyu(hc_ts['tSNE2'], mci_ts['tSNE2'])
            
            print(f"  Mann-Whitney U检验:")
            print(f"    tSNE1: U={u_stat_1:.3f}, p={u_p_1:.6f}")
            print(f"    tSNE2: U={u_stat_2:.3f}, p={u_p_2:.6f}")
            
            results['ts'] = {
                'ttest_tsne1': (t_stat_1, p_val_1),
                'ttest_tsne2': (t_stat_2, p_val_2),
                'mannwhitney_tsne1': (u_stat_1, u_p_1),
                'mannwhitney_tsne2': (u_stat_2, u_p_2)
            }
        
        return results
    
    def classification_analysis(self):
        """进行分类性能分析"""
        print("\n=== 分类性能分析 ===")
        
        classifiers = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', random_state=42, probability=True),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        results = {}
        
        # 分析FC数据
        if self.fc_data is not None:
            print("\n1. FC数据分类性能:")
            X_fc = self.fc_data[['tSNE1', 'tSNE2']].values
            y_fc = (self.fc_data['Group'] == 'MCI').astype(int)
            
            fc_results = {}
            for name, clf in classifiers.items():
                cv_scores = cross_val_score(clf, X_fc, y_fc, cv=5, scoring='accuracy')
                fc_results[name] = {
                    'mean_accuracy': cv_scores.mean(),
                    'std_accuracy': cv_scores.std(),
                    'scores': cv_scores
                }
                print(f"  {name}: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
            results['fc'] = fc_results
        
        # 分析时间序列数据
        if self.ts_data is not None:
            print("\n2. 时间序列数据分类性能:")
            X_ts = self.ts_data[['tSNE1', 'tSNE2']].values
            y_ts = (self.ts_data['Group'] == 'MCI').astype(int)
            
            ts_results = {}
            for name, clf in classifiers.items():
                cv_scores = cross_val_score(clf, X_ts, y_ts, cv=5, scoring='accuracy')
                ts_results[name] = {
                    'mean_accuracy': cv_scores.mean(),
                    'std_accuracy': cv_scores.std(),
                    'scores': cv_scores
                }
                print(f"  {name}: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
            results['ts'] = ts_results
        
        return results
    
    def create_comprehensive_visualization(self, save_path='results_advanced'):
        """创建综合可视化"""
        print("\n正在生成综合可视化...")
        
        os.makedirs(save_path, exist_ok=True)
        
        # 创建大型综合图
        fig = plt.figure(figsize=(20, 15))
        
        # 1. FC数据分布对比
        if self.fc_data is not None:
            ax1 = plt.subplot(3, 4, 1)
            hc_fc = self.fc_data[self.fc_data['Group'] == 'HC']
            mci_fc = self.fc_data[self.fc_data['Group'] == 'MCI']
            
            plt.scatter(hc_fc['tSNE1'], hc_fc['tSNE2'], c='#2E86AB', alpha=0.6, label='HC', s=30)
            plt.scatter(mci_fc['tSNE1'], mci_fc['tSNE2'], c='#F24236', alpha=0.6, label='MCI', s=30)
            plt.title('FC Matrix Analysis', fontsize=12)
            plt.xlabel('t-SNE 1')
            plt.ylabel('t-SNE 2')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 2. 时间序列数据分布对比
        if self.ts_data is not None:
            ax2 = plt.subplot(3, 4, 2)
            hc_ts = self.ts_data[self.ts_data['Group'] == 'HC']
            mci_ts = self.ts_data[self.ts_data['Group'] == 'MCI']
            
            plt.scatter(hc_ts['tSNE1'], hc_ts['tSNE2'], c='#2E86AB', alpha=0.6, label='HC', s=30)
            plt.scatter(mci_ts['tSNE1'], mci_ts['tSNE2'], c='#F24236', alpha=0.6, label='MCI', s=30)
            plt.title('Time Series Analysis', fontsize=12)
            plt.xlabel('t-SNE 1')
            plt.ylabel('t-SNE 2')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 3. FC数据密度图
        if self.fc_data is not None:
            ax3 = plt.subplot(3, 4, 3)
            sns.kdeplot(data=self.fc_data, x='tSNE1', y='tSNE2', hue='Group', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'}, alpha=0.6)
            plt.title('FC Density Plot', fontsize=12)
            plt.grid(True, alpha=0.3)
        
        # 4. 时间序列密度图
        if self.ts_data is not None:
            ax4 = plt.subplot(3, 4, 4)
            sns.kdeplot(data=self.ts_data, x='tSNE1', y='tSNE2', hue='Group', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'}, alpha=0.6)
            plt.title('Time Series Density Plot', fontsize=12)
            plt.grid(True, alpha=0.3)
        
        # 5-8. 分布比较
        if self.fc_data is not None:
            ax5 = plt.subplot(3, 4, 5)
            sns.boxplot(data=self.fc_data, x='Group', y='tSNE1', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'})
            plt.title('FC tSNE1 Distribution', fontsize=12)
            
            ax6 = plt.subplot(3, 4, 6)
            sns.boxplot(data=self.fc_data, x='Group', y='tSNE2', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'})
            plt.title('FC tSNE2 Distribution', fontsize=12)
        
        if self.ts_data is not None:
            ax7 = plt.subplot(3, 4, 7)
            sns.boxplot(data=self.ts_data, x='Group', y='tSNE1', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'})
            plt.title('TS tSNE1 Distribution', fontsize=12)
            
            ax8 = plt.subplot(3, 4, 8)
            sns.boxplot(data=self.ts_data, x='Group', y='tSNE2', 
                       palette={'HC': '#2E86AB', 'MCI': '#F24236'})
            plt.title('TS tSNE2 Distribution', fontsize=12)
        
        # 9-12. 直方图比较
        if self.fc_data is not None:
            ax9 = plt.subplot(3, 4, 9)
            hc_fc = self.fc_data[self.fc_data['Group'] == 'HC']
            mci_fc = self.fc_data[self.fc_data['Group'] == 'MCI']
            plt.hist(hc_fc['tSNE1'], alpha=0.6, label='HC', color='#2E86AB', bins=20)
            plt.hist(mci_fc['tSNE1'], alpha=0.6, label='MCI', color='#F24236', bins=20)
            plt.title('FC tSNE1 Histogram', fontsize=12)
            plt.legend()
            
            ax10 = plt.subplot(3, 4, 10)
            plt.hist(hc_fc['tSNE2'], alpha=0.6, label='HC', color='#2E86AB', bins=20)
            plt.hist(mci_fc['tSNE2'], alpha=0.6, label='MCI', color='#F24236', bins=20)
            plt.title('FC tSNE2 Histogram', fontsize=12)
            plt.legend()
        
        if self.ts_data is not None:
            ax11 = plt.subplot(3, 4, 11)
            hc_ts = self.ts_data[self.ts_data['Group'] == 'HC']
            mci_ts = self.ts_data[self.ts_data['Group'] == 'MCI']
            plt.hist(hc_ts['tSNE1'], alpha=0.6, label='HC', color='#2E86AB', bins=20)
            plt.hist(mci_ts['tSNE1'], alpha=0.6, label='MCI', color='#F24236', bins=20)
            plt.title('TS tSNE1 Histogram', fontsize=12)
            plt.legend()
            
            ax12 = plt.subplot(3, 4, 12)
            plt.hist(hc_ts['tSNE2'], alpha=0.6, label='HC', color='#2E86AB', bins=20)
            plt.hist(mci_ts['tSNE2'], alpha=0.6, label='MCI', color='#F24236', bins=20)
            plt.title('TS tSNE2 Histogram', fontsize=12)
            plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'comprehensive_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"综合可视化已保存到 {save_path}/comprehensive_analysis.png")
    
    def generate_detailed_report(self, stats_results, class_results, save_path='results_advanced'):
        """生成详细分析报告"""
        print("\n正在生成详细报告...")
        
        os.makedirs(save_path, exist_ok=True)
        
        report = []
        report.append("# 高级脑功能连接数据分析报告\n")
        report.append("## 统计分析结果\n")
        
        if 'fc' in stats_results:
            report.append("### FC矩阵数据统计检验\n")
            fc_stats = stats_results['fc']
            report.append("| 检验方法 | 维度 | 统计量 | p值 | 显著性 |\n")
            report.append("|---------|------|--------|-----|--------|\n")
            
            for test_name, (stat, p) in fc_stats.items():
                significance = "***" if p < 0.001 else "**" if p < 0.01 else "*" if p < 0.05 else "ns"
                dimension = "tSNE1" if "tsne1" in test_name else "tSNE2"
                test_type = "t-test" if "ttest" in test_name else "Mann-Whitney U"
                report.append(f"| {test_type} | {dimension} | {stat:.3f} | {p:.6f} | {significance} |\n")
            report.append("\n")
        
        if 'ts' in stats_results:
            report.append("### 时间序列数据统计检验\n")
            ts_stats = stats_results['ts']
            report.append("| 检验方法 | 维度 | 统计量 | p值 | 显著性 |\n")
            report.append("|---------|------|--------|-----|--------|\n")
            
            for test_name, (stat, p) in ts_stats.items():
                significance = "***" if p < 0.001 else "**" if p < 0.01 else "*" if p < 0.05 else "ns"
                dimension = "tSNE1" if "tsne1" in test_name else "tSNE2"
                test_type = "t-test" if "ttest" in test_name else "Mann-Whitney U"
                report.append(f"| {test_type} | {dimension} | {stat:.3f} | {p:.6f} | {significance} |\n")
            report.append("\n")
        
        report.append("## 分类性能分析\n")
        
        if 'fc' in class_results:
            report.append("### FC矩阵数据分类结果\n")
            report.append("| 分类器 | 平均准确率 | 标准差 |\n")
            report.append("|--------|------------|--------|\n")
            
            for clf_name, results in class_results['fc'].items():
                report.append(f"| {clf_name} | {results['mean_accuracy']:.3f} | {results['std_accuracy']:.3f} |\n")
            report.append("\n")
        
        if 'ts' in class_results:
            report.append("### 时间序列数据分类结果\n")
            report.append("| 分类器 | 平均准确率 | 标准差 |\n")
            report.append("|--------|------------|--------|\n")
            
            for clf_name, results in class_results['ts'].items():
                report.append(f"| {clf_name} | {results['mean_accuracy']:.3f} | {results['std_accuracy']:.3f} |\n")
            report.append("\n")
        
        # 保存报告
        with open(os.path.join(save_path, 'detailed_analysis_report.md'), 'w', encoding='utf-8') as f:
            f.writelines(report)
        
        print(f"详细报告已保存到 {save_path}/detailed_analysis_report.md")

def main():
    """主函数"""
    print("=== 高级脑功能连接数据分析 ===")
    
    # 初始化分析器
    analyzer = AdvancedBrainAnalyzer()
    
    # 加载数据
    analyzer.load_analysis_results()
    
    # 统计分析
    stats_results = analyzer.statistical_analysis()
    
    # 分类性能分析
    class_results = analyzer.classification_analysis()
    
    # 综合可视化
    analyzer.create_comprehensive_visualization()
    
    # 生成详细报告
    analyzer.generate_detailed_report(stats_results, class_results)
    
    print("\n高级分析完成！")

if __name__ == "__main__":
    main()
