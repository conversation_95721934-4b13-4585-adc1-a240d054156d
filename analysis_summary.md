# 脑功能连接数据分析与可视化总结报告

## 项目概述

本项目对ADNI数据集中的HC（健康对照组）和MCI（轻度认知障碍组）的脑功能连接数据进行了全面分析，包括：

1. **功能连接（FC）矩阵分析**：基于Fisher Z变换后的116×116功能连接矩阵
2. **时间序列分析**：基于116个脑区的时间序列数据的时域统计特征
3. **t-SNE降维可视化**：用于探索两组之间的差异模式

## 数据概况

### 数据结构
- **FC矩阵数据**：
  - HC组：283个样本，每个样本为116×116的功能连接矩阵
  - MCI组：213个样本，每个样本为116×116的功能连接矩阵
  
- **时间序列数据**：
  - HC组：283个样本，时间点数量变化（130-205个时间点）
  - MCI组：213个样本，时间点数量变化（130-190个时间点）
  - 所有样本均包含116个脑区的时间序列

### 数据质量
- 所有FC矩阵数据成功加载，经过无穷大值和NaN值处理
- 时间序列数据长度不一致，但都包含完整的116个脑区信息
- 数据预处理包括标准化和异常值处理

## 分析方法

### 1. FC矩阵特征提取
- **连接特征**：提取上三角矩阵的6670个连接值
- **网络特征**：计算全局统计特征（均值、标准差、偏度、峰度等）
- **节点特征**：计算每个节点的度中心性统计

### 2. 时间序列特征提取
- **全局特征**：所有脑区的整体统计特征
- **区域特征**：每个脑区的详细时域统计特征
  - 基本统计：均值、标准差、方差、最值、范围
  - 高阶统计：偏度、峰度、百分位数、四分位距
  - 能量特征：总能量、均方根值
  - 动态特征：零交叉率、峰值计数
  - 变异系数：标准化的变异性度量
- **相关性特征**：脑区间相关性矩阵的统计特征

### 3. 降维与可视化
- **PCA预处理**：将高维特征降至50维以提高t-SNE效率
- **t-SNE降维**：将特征映射到2维空间进行可视化
- **多角度可视化**：散点图、密度图、分布直方图、箱线图

## 主要发现

### FC矩阵分析结果
- **样本数量**：HC组283个，MCI组213个
- **特征维度**：6682维（包含连接特征和网络特征）
- **PCA解释方差比**：63.7%
- **组间分离度**：
  - HC组中心：(2.628, -0.738)
  - MCI组中心：(-0.258, 0.082)
  - 组间中心距离：3.000

### 时间序列分析结果
- **样本数量**：HC组283个，MCI组213个
- **特征维度**：2101维（包含全局、区域和相关性特征）
- **PCA解释方差比**：80.2%
- **组间分离度**：
  - HC组中心：(-1.537, 1.282)
  - MCI组中心：(2.548, -3.989)
  - 组间中心距离：6.669

## 关键观察

### 1. 分离效果比较
- **时间序列特征**显示出更好的组间分离效果（距离6.669 vs 3.000）
- **时间序列特征**的PCA解释方差比更高（80.2% vs 63.7%）
- 这表明时间序列的时域统计特征可能更有效地捕捉HC和MCI之间的差异

### 2. 特征维度
- FC矩阵特征维度更高（6682维），主要由连接矩阵贡献
- 时间序列特征维度相对较低（2101维），但信息密度更高

### 3. 数据质量
- 两种分析都成功处理了数据中的异常值
- 时间序列长度的不一致性通过灵活的特征提取方法得到解决

## 生成的文件

### 结果文件
1. **results/fc_tsne_visualization.png** - FC矩阵分析可视化结果
2. **results/fc_tsne_results.csv** - FC矩阵分析数据结果
3. **results_adaptive/adaptive_tsne_fc_only.png** - 自适应FC分析可视化
4. **results_adaptive/adaptive_tsne_fc_only.csv** - 自适应FC分析数据
5. **results_timeseries/timeseries_tsne_visualization.png** - 时间序列分析可视化
6. **results_timeseries/timeseries_tsne_results.csv** - 时间序列分析数据

### 分析脚本
1. **fc_analysis_simplified.py** - FC矩阵专用分析脚本
2. **adaptive_brain_analysis.py** - 自适应分析脚本
3. **time_series_analysis.py** - 时间序列专用分析脚本
4. **brain_connectivity_analysis.py** - 原始完整分析脚本
5. **complete_brain_analysis.py** - 完整结合分析脚本

## 技术细节

### 数据预处理
- Fisher Z变换后的FC矩阵处理无穷大值和NaN值
- 时间序列数据标准化和异常值检测
- 特征标准化确保不同尺度特征的公平比较

### 降维策略
- 使用PCA作为t-SNE的预处理步骤
- 困惑度参数自适应调整
- 多次迭代确保收敛

### 可视化设计
- 多子图展示不同角度的分析结果
- 颜色编码区分HC和MCI组
- 统计信息叠加提供定量评估

## 结论与建议

### 主要结论
1. **时间序列特征**在区分HC和MCI方面表现更优
2. **两种特征类型**都能有效捕捉组间差异
3. **数据质量良好**，适合进一步的机器学习分析

### 后续建议
1. **特征融合**：结合FC和时间序列特征可能获得更好效果
2. **机器学习分类**：基于提取的特征训练分类器
3. **特征选择**：识别最具判别性的特征子集
4. **交叉验证**：评估模型的泛化能力
5. **临床解释**：将发现的模式与神经科学知识结合

## 技术规格

- **编程语言**：Python 3.x
- **主要库**：NumPy, Pandas, Scikit-learn, Matplotlib, Seaborn
- **降维方法**：PCA + t-SNE
- **特征数量**：FC矩阵6682维，时间序列2101维
- **样本总数**：496个（HC: 283, MCI: 213）

---

*报告生成时间：2025年*
*分析完成状态：成功*
