#!/usr/bin/env python3
"""
脑功能连接数据分析与可视化
分析HC组和MCI组的功能连接矩阵和时间序列数据
计算时域统计特征，结合FC矩阵进行t-SNE降维可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import os
import glob
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class BrainConnectivityAnalyzer:
    def __init__(self, data_dir='data', node_features_dir='node_features'):
        """
        初始化分析器
        
        Parameters:
        -----------
        data_dir : str
            FC矩阵数据目录
        node_features_dir : str
            时间序列数据目录
        """
        self.data_dir = data_dir
        self.node_features_dir = node_features_dir
        self.hc_fc_data = []
        self.mci_fc_data = []
        self.hc_ts_data = []
        self.mci_ts_data = []
        self.hc_features = []
        self.mci_features = []
        
    def load_data(self):
        """加载HC和MCI组的数据"""
        print("正在加载数据...")

        # 加载HC组FC矩阵
        hc_fc_files = glob.glob(os.path.join(self.data_dir, 'HC', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(hc_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.hc_fc_data.append(fc_matrix)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")

        # 加载MCI组FC矩阵
        mci_fc_files = glob.glob(os.path.join(self.data_dir, 'MCI', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(mci_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.mci_fc_data.append(fc_matrix)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")

        # 加载HC组时间序列
        hc_ts_files = glob.glob(os.path.join(self.node_features_dir, 'HC', 'ROISignals_*.txt'))
        for file in sorted(hc_ts_files):
            try:
                ts_data = np.loadtxt(file)
                if ts_data.shape == (131, 116):
                    self.hc_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")

        # 加载MCI组时间序列
        mci_ts_files = glob.glob(os.path.join(self.node_features_dir, 'MCI', 'ROISignals_*.txt'))
        for file in sorted(mci_ts_files):
            try:
                ts_data = np.loadtxt(file)
                if ts_data.shape == (131, 116):
                    self.mci_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")

        print(f"成功加载 HC组: {len(self.hc_fc_data)} 个FC矩阵, {len(self.hc_ts_data)} 个时间序列")
        print(f"成功加载 MCI组: {len(self.mci_fc_data)} 个FC矩阵, {len(self.mci_ts_data)} 个时间序列")

        # 匹配FC矩阵和时间序列数据
        self._match_fc_and_ts_data()

    def _match_fc_and_ts_data(self):
        """匹配FC矩阵和时间序列数据，确保它们来自同一被试"""
        # 提取HC组文件ID
        hc_fc_ids = []
        for file in glob.glob(os.path.join(self.data_dir, 'HC', 'ROICorrelation_FisherZ_*.txt')):
            filename = os.path.basename(file)
            subject_id = filename.replace('ROICorrelation_FisherZ_', '').replace('.txt', '')
            hc_fc_ids.append(subject_id)

        hc_ts_ids = []
        for file in glob.glob(os.path.join(self.node_features_dir, 'HC', 'ROISignals_*.txt')):
            filename = os.path.basename(file)
            subject_id = filename.replace('ROISignals_', '').replace('.txt', '')
            hc_ts_ids.append(subject_id)

        # 找到HC组的共同ID
        hc_common_ids = list(set(hc_fc_ids) & set(hc_ts_ids))
        hc_common_ids.sort()

        # 提取MCI组文件ID
        mci_fc_ids = []
        for file in glob.glob(os.path.join(self.data_dir, 'MCI', 'ROICorrelation_FisherZ_*.txt')):
            filename = os.path.basename(file)
            subject_id = filename.replace('ROICorrelation_FisherZ_', '').replace('.txt', '')
            mci_fc_ids.append(subject_id)

        mci_ts_ids = []
        for file in glob.glob(os.path.join(self.node_features_dir, 'MCI', 'ROISignals_*.txt')):
            filename = os.path.basename(file)
            subject_id = filename.replace('ROISignals_', '').replace('.txt', '')
            mci_ts_ids.append(subject_id)

        # 找到MCI组的共同ID
        mci_common_ids = list(set(mci_fc_ids) & set(mci_ts_ids))
        mci_common_ids.sort()

        print(f"HC组匹配的被试数: {len(hc_common_ids)}")
        print(f"MCI组匹配的被试数: {len(mci_common_ids)}")

        # 重新加载匹配的数据
        self.hc_fc_data = []
        self.hc_ts_data = []
        self.mci_fc_data = []
        self.mci_ts_data = []

        # 加载匹配的HC组数据
        for subject_id in hc_common_ids:
            fc_file = os.path.join(self.data_dir, 'HC', f'ROICorrelation_FisherZ_{subject_id}.txt')
            ts_file = os.path.join(self.node_features_dir, 'HC', f'ROISignals_{subject_id}.txt')

            try:
                fc_matrix = np.loadtxt(fc_file)
                ts_data = np.loadtxt(ts_file)
                if fc_matrix.shape == (116, 116) and ts_data.shape == (131, 116):
                    self.hc_fc_data.append(fc_matrix)
                    self.hc_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载被试 {subject_id} 数据时出错: {e}")

        # 加载匹配的MCI组数据
        for subject_id in mci_common_ids:
            fc_file = os.path.join(self.data_dir, 'MCI', f'ROICorrelation_FisherZ_{subject_id}.txt')
            ts_file = os.path.join(self.node_features_dir, 'MCI', f'ROISignals_{subject_id}.txt')

            try:
                fc_matrix = np.loadtxt(fc_file)
                ts_data = np.loadtxt(ts_file)
                if fc_matrix.shape == (116, 116) and ts_data.shape == (131, 116):
                    self.mci_fc_data.append(fc_matrix)
                    self.mci_ts_data.append(ts_data)
            except Exception as e:
                print(f"加载被试 {subject_id} 数据时出错: {e}")

        print(f"最终加载 HC组: {len(self.hc_fc_data)} 个匹配的数据对")
        print(f"最终加载 MCI组: {len(self.mci_fc_data)} 个匹配的数据对")

    def calculate_temporal_features(self, ts_data):
        """
        计算时间序列的时域统计特征
        
        Parameters:
        -----------
        ts_data : numpy.ndarray
            时间序列数据 (time_points, regions)
            
        Returns:
        --------
        features : numpy.ndarray
            时域特征向量
        """
        features = []
        
        # 对每个脑区计算统计特征
        for region in range(ts_data.shape[1]):
            region_ts = ts_data[:, region]
            
            # 基本统计特征
            mean_val = np.mean(region_ts)
            std_val = np.std(region_ts)
            var_val = np.var(region_ts)
            min_val = np.min(region_ts)
            max_val = np.max(region_ts)
            range_val = max_val - min_val
            
            # 高阶统计特征
            skewness = stats.skew(region_ts)
            kurtosis = stats.kurtosis(region_ts)
            
            # 百分位数
            q25 = np.percentile(region_ts, 25)
            q50 = np.percentile(region_ts, 50)  # 中位数
            q75 = np.percentile(region_ts, 75)
            iqr = q75 - q25  # 四分位距
            
            # 能量相关特征
            energy = np.sum(region_ts ** 2)
            rms = np.sqrt(np.mean(region_ts ** 2))
            
            # 变异系数
            cv = std_val / abs(mean_val) if mean_val != 0 else 0
            
            features.extend([
                mean_val, std_val, var_val, min_val, max_val, range_val,
                skewness, kurtosis, q25, q50, q75, iqr, energy, rms, cv
            ])
        
        return np.array(features)
    
    def extract_fc_features(self, fc_matrix):
        """
        提取FC矩阵特征
        
        Parameters:
        -----------
        fc_matrix : numpy.ndarray
            功能连接矩阵
            
        Returns:
        --------
        features : numpy.ndarray
            FC特征向量
        """
        # 提取上三角矩阵（去除对角线）
        upper_tri_indices = np.triu_indices(fc_matrix.shape[0], k=1)
        fc_features = fc_matrix[upper_tri_indices]
        
        return fc_features
    
    def process_features(self):
        """处理所有被试的特征"""
        print("正在计算时域特征...")
        
        # 计算HC组特征
        for i, (fc_data, ts_data) in enumerate(zip(self.hc_fc_data, self.hc_ts_data)):
            # 时域特征
            temporal_features = self.calculate_temporal_features(ts_data)
            # FC特征
            fc_features = self.extract_fc_features(fc_data)
            # 合并特征
            combined_features = np.concatenate([temporal_features, fc_features])
            self.hc_features.append(combined_features)
        
        # 计算MCI组特征
        for i, (fc_data, ts_data) in enumerate(zip(self.mci_fc_data, self.mci_ts_data)):
            # 时域特征
            temporal_features = self.calculate_temporal_features(ts_data)
            # FC特征
            fc_features = self.extract_fc_features(fc_data)
            # 合并特征
            combined_features = np.concatenate([temporal_features, fc_features])
            self.mci_features.append(combined_features)
        
        print(f"HC组特征维度: {len(self.hc_features)} × {len(self.hc_features[0]) if self.hc_features else 0}")
        print(f"MCI组特征维度: {len(self.mci_features)} × {len(self.mci_features[0]) if self.mci_features else 0}")
    
    def perform_tsne_analysis(self, perplexity=30, n_components=2, random_state=42):
        """
        执行t-SNE降维分析
        
        Parameters:
        -----------
        perplexity : int
            t-SNE困惑度参数
        n_components : int
            降维后的维度
        random_state : int
            随机种子
        """
        print("正在进行t-SNE降维分析...")
        
        # 合并所有特征
        all_features = np.array(self.hc_features + self.mci_features)
        labels = ['HC'] * len(self.hc_features) + ['MCI'] * len(self.mci_features)
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(all_features)
        
        # 先用PCA降维以提高t-SNE效率
        if features_scaled.shape[1] > 50:
            print("使用PCA预处理...")
            pca = PCA(n_components=50, random_state=random_state)
            features_pca = pca.fit_transform(features_scaled)
            print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
        else:
            features_pca = features_scaled
        
        # t-SNE降维
        tsne = TSNE(n_components=n_components, perplexity=perplexity, 
                   random_state=random_state, n_iter=1000)
        features_tsne = tsne.fit_transform(features_pca)
        
        return features_tsne, labels
    
    def visualize_results(self, features_tsne, labels, save_path='results'):
        """
        可视化t-SNE结果
        
        Parameters:
        -----------
        features_tsne : numpy.ndarray
            t-SNE降维后的特征
        labels : list
            样本标签
        save_path : str
            保存路径
        """
        print("正在生成可视化结果...")
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'tSNE1': features_tsne[:, 0],
            'tSNE2': features_tsne[:, 1],
            'Group': labels
        })
        
        # 设置图形样式
        plt.style.use('default')
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 子图1: 散点图
        colors = {'HC': '#2E86AB', 'MCI': '#F24236'}
        for group in ['HC', 'MCI']:
            group_data = df[df['Group'] == group]
            axes[0].scatter(group_data['tSNE1'], group_data['tSNE2'], 
                          c=colors[group], label=group, alpha=0.7, s=50)
        
        axes[0].set_xlabel('t-SNE Component 1', fontsize=12)
        axes[0].set_ylabel('t-SNE Component 2', fontsize=12)
        axes[0].set_title('HC vs MCI Groups - t-SNE Visualization', fontsize=14)
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 子图2: 密度图
        sns.scatterplot(data=df, x='tSNE1', y='tSNE2', hue='Group', 
                       palette=colors, alpha=0.7, s=50, ax=axes[1])
        axes[1].set_title('HC vs MCI Groups - Density Plot', fontsize=14)
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'tsne_visualization.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存数据
        df.to_csv(os.path.join(save_path, 'tsne_results.csv'), index=False)
        print(f"结果已保存到 {save_path} 目录")
        
        return df

def main():
    """主函数"""
    print("=== 脑功能连接数据分析与可视化 ===")
    
    # 初始化分析器
    analyzer = BrainConnectivityAnalyzer()
    
    # 加载数据
    analyzer.load_data()
    
    # 处理特征
    analyzer.process_features()
    
    # t-SNE分析
    features_tsne, labels = analyzer.perform_tsne_analysis()
    
    # 可视化结果
    results_df = analyzer.visualize_results(features_tsne, labels)
    
    # 打印统计信息
    print("\n=== 分析结果统计 ===")
    print(f"HC组样本数: {sum(1 for label in labels if label == 'HC')}")
    print(f"MCI组样本数: {sum(1 for label in labels if label == 'MCI')}")
    print(f"特征维度: {len(analyzer.hc_features[0]) if analyzer.hc_features else 0}")
    print("分析完成！")

if __name__ == "__main__":
    main()
