#!/usr/bin/env python3
"""
特征重要性分析和ROC曲线分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_predict
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_curve, auc, classification_report, confusion_matrix
from sklearn.inspection import permutation_importance
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FeatureImportanceAnalyzer:
    def __init__(self):
        """初始化特征重要性分析器"""
        self.fc_data = None
        self.ts_data = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        
        try:
            self.fc_data = pd.read_csv('results/fc_tsne_results.csv')
            print(f"FC数据加载成功: {len(self.fc_data)} 个样本")
        except:
            print("FC数据加载失败")
            
        try:
            self.ts_data = pd.read_csv('results_timeseries/timeseries_tsne_results.csv')
            print(f"时间序列数据加载成功: {len(self.ts_data)} 个样本")
        except:
            print("时间序列数据加载失败")
    
    def analyze_feature_importance(self, data, data_name):
        """分析特征重要性"""
        print(f"\n=== {data_name} 特征重要性分析 ===")
        
        X = data[['tSNE1', 'tSNE2']].values
        y = (data['Group'] == 'MCI').astype(int)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 随机森林特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_train_scaled, y_train)
        
        rf_importance = rf.feature_importances_
        print(f"随机森林特征重要性:")
        print(f"  tSNE1: {rf_importance[0]:.3f}")
        print(f"  tSNE2: {rf_importance[1]:.3f}")
        
        # 排列重要性
        perm_importance = permutation_importance(rf, X_test_scaled, y_test, n_repeats=10, random_state=42)
        print(f"排列重要性:")
        print(f"  tSNE1: {perm_importance.importances_mean[0]:.3f} ± {perm_importance.importances_std[0]:.3f}")
        print(f"  tSNE2: {perm_importance.importances_mean[1]:.3f} ± {perm_importance.importances_std[1]:.3f}")
        
        return {
            'rf_importance': rf_importance,
            'perm_importance_mean': perm_importance.importances_mean,
            'perm_importance_std': perm_importance.importances_std,
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test
        }
    
    def create_roc_analysis(self, data, data_name, results):
        """创建ROC曲线分析"""
        print(f"\n=== {data_name} ROC曲线分析 ===")
        
        X_train = results['X_train']
        X_test = results['X_test']
        y_train = results['y_train']
        y_test = results['y_test']
        
        # 定义分类器
        classifiers = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', random_state=42, probability=True),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        plt.figure(figsize=(12, 8))
        
        # 为每个分类器绘制ROC曲线
        for i, (name, clf) in enumerate(classifiers.items()):
            # 训练模型
            clf.fit(X_train, y_train)
            
            # 预测概率
            y_pred_proba = clf.predict_proba(X_test)[:, 1]
            
            # 计算ROC曲线
            fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            
            # 绘制ROC曲线
            plt.subplot(2, 2, i+1)
            plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
            plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
            plt.xlim([0.0, 1.0])
            plt.ylim([0.0, 1.05])
            plt.xlabel('False Positive Rate')
            plt.ylabel('True Positive Rate')
            plt.title(f'{name} - {data_name}')
            plt.legend(loc="lower right")
            plt.grid(True, alpha=0.3)
            
            print(f"{name}: AUC = {roc_auc:.3f}")
        
        # 比较所有分类器的ROC曲线
        plt.subplot(2, 2, 4)
        colors = ['darkorange', 'darkgreen', 'darkblue']
        
        for i, (name, clf) in enumerate(classifiers.items()):
            clf.fit(X_train, y_train)
            y_pred_proba = clf.predict_proba(X_test)[:, 1]
            fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            
            plt.plot(fpr, tpr, color=colors[i], lw=2, 
                    label=f'{name} (AUC = {roc_auc:.3f})')
        
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'ROC Comparison - {data_name}')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'results_advanced/roc_analysis_{data_name.lower().replace(" ", "_")}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_feature_importance_visualization(self, fc_results, ts_results):
        """创建特征重要性可视化"""
        print("\n正在生成特征重要性可视化...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # FC数据随机森林重要性
        if fc_results:
            ax1 = axes[0, 0]
            features = ['tSNE1', 'tSNE2']
            importance = fc_results['rf_importance']
            bars1 = ax1.bar(features, importance, color=['#2E86AB', '#F24236'], alpha=0.7)
            ax1.set_title('FC Data - Random Forest Feature Importance')
            ax1.set_ylabel('Importance')
            ax1.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, imp in zip(bars1, importance):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                        f'{imp:.3f}', ha='center', va='bottom')
        
        # FC数据排列重要性
        if fc_results:
            ax2 = axes[0, 1]
            mean_imp = fc_results['perm_importance_mean']
            std_imp = fc_results['perm_importance_std']
            bars2 = ax2.bar(features, mean_imp, yerr=std_imp, 
                           color=['#2E86AB', '#F24236'], alpha=0.7, capsize=5)
            ax2.set_title('FC Data - Permutation Feature Importance')
            ax2.set_ylabel('Importance')
            ax2.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, imp, std in zip(bars2, mean_imp, std_imp):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01, 
                        f'{imp:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 时间序列数据随机森林重要性
        if ts_results:
            ax3 = axes[1, 0]
            importance = ts_results['rf_importance']
            bars3 = ax3.bar(features, importance, color=['#2E86AB', '#F24236'], alpha=0.7)
            ax3.set_title('Time Series Data - Random Forest Feature Importance')
            ax3.set_ylabel('Importance')
            ax3.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, imp in zip(bars3, importance):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                        f'{imp:.3f}', ha='center', va='bottom')
        
        # 时间序列数据排列重要性
        if ts_results:
            ax4 = axes[1, 1]
            mean_imp = ts_results['perm_importance_mean']
            std_imp = ts_results['perm_importance_std']
            bars4 = ax4.bar(features, mean_imp, yerr=std_imp, 
                           color=['#2E86AB', '#F24236'], alpha=0.7, capsize=5)
            ax4.set_title('Time Series Data - Permutation Feature Importance')
            ax4.set_ylabel('Importance')
            ax4.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, imp, std in zip(bars4, mean_imp, std_imp):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01, 
                        f'{imp:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        plt.savefig('results_advanced/feature_importance_analysis.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        print("特征重要性可视化已保存到 results_advanced/feature_importance_analysis.png")
    
    def create_confusion_matrices(self, fc_results, ts_results):
        """创建混淆矩阵"""
        print("\n正在生成混淆矩阵...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        classifiers = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        # FC数据混淆矩阵
        if fc_results:
            for i, (name, clf) in enumerate(classifiers.items()):
                X_train = fc_results['X_train']
                X_test = fc_results['X_test']
                y_train = fc_results['y_train']
                y_test = fc_results['y_test']
                
                clf.fit(X_train, y_train)
                y_pred = clf.predict(X_test)
                
                cm = confusion_matrix(y_test, y_pred)
                
                ax = axes[0, i]
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                           xticklabels=['HC', 'MCI'], yticklabels=['HC', 'MCI'], ax=ax)
                ax.set_title(f'FC Data - {name}')
                ax.set_xlabel('Predicted')
                ax.set_ylabel('Actual')
        
        # 时间序列数据混淆矩阵
        if ts_results:
            for i, (name, clf) in enumerate(classifiers.items()):
                X_train = ts_results['X_train']
                X_test = ts_results['X_test']
                y_train = ts_results['y_train']
                y_test = ts_results['y_test']
                
                clf.fit(X_train, y_train)
                y_pred = clf.predict(X_test)
                
                cm = confusion_matrix(y_test, y_pred)
                
                ax = axes[1, i]
                sns.heatmap(cm, annot=True, fmt='d', cmap='Reds', 
                           xticklabels=['HC', 'MCI'], yticklabels=['HC', 'MCI'], ax=ax)
                ax.set_title(f'Time Series Data - {name}')
                ax.set_xlabel('Predicted')
                ax.set_ylabel('Actual')
        
        plt.tight_layout()
        plt.savefig('results_advanced/confusion_matrices.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        print("混淆矩阵已保存到 results_advanced/confusion_matrices.png")

def main():
    """主函数"""
    print("=== 特征重要性和ROC曲线分析 ===")
    
    # 创建结果目录
    os.makedirs('results_advanced', exist_ok=True)
    
    # 初始化分析器
    analyzer = FeatureImportanceAnalyzer()
    
    # 加载数据
    analyzer.load_data()
    
    fc_results = None
    ts_results = None
    
    # 分析FC数据
    if analyzer.fc_data is not None:
        fc_results = analyzer.analyze_feature_importance(analyzer.fc_data, "FC Data")
        analyzer.create_roc_analysis(analyzer.fc_data, "FC Data", fc_results)
    
    # 分析时间序列数据
    if analyzer.ts_data is not None:
        ts_results = analyzer.analyze_feature_importance(analyzer.ts_data, "Time Series Data")
        analyzer.create_roc_analysis(analyzer.ts_data, "Time Series Data", ts_results)
    
    # 创建特征重要性可视化
    if fc_results or ts_results:
        analyzer.create_feature_importance_visualization(fc_results, ts_results)
    
    # 创建混淆矩阵
    if fc_results or ts_results:
        analyzer.create_confusion_matrices(fc_results, ts_results)
    
    print("\n特征重要性和ROC曲线分析完成！")

if __name__ == "__main__":
    main()
