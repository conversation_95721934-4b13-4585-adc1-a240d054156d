# 脑功能连接数据分析项目 - 文件索引

## 📁 项目结构总览

```
data_visualize/
├── 📊 核心分析脚本
│   ├── brain_connectivity_analysis.py          # 原始完整分析脚本
│   ├── fc_analysis_simplified.py               # FC矩阵专用分析 ⭐
│   ├── time_series_analysis.py                 # 时间序列专用分析 ⭐
│   ├── adaptive_brain_analysis.py              # 自适应分析框架
│   ├── complete_brain_analysis.py              # 完整结合分析脚本
│   ├── advanced_analysis.py                    # 高级统计和分类分析 ⭐
│   └── feature_importance_analysis.py          # 特征重要性和ROC分析 ⭐
│
├── 📈 基础分析结果
│   ├── results/
│   │   ├── fc_tsne_visualization.png           # FC矩阵t-SNE可视化
│   │   └── fc_tsne_results.csv                 # FC矩阵分析数据
│   │
│   ├── results_adaptive/
│   │   ├── adaptive_tsne_fc_only.png           # 自适应FC分析可视化
│   │   └── adaptive_tsne_fc_only.csv           # 自适应FC分析数据
│   │
│   └── results_timeseries/
│       ├── timeseries_tsne_visualization.png   # 时间序列t-SNE可视化 ⭐
│       └── timeseries_tsne_results.csv         # 时间序列分析数据 ⭐
│
├── 🔬 高级分析结果
│   └── results_advanced/
│       ├── comprehensive_analysis.png          # 综合对比分析 ⭐
│       ├── feature_importance_analysis.png     # 特征重要性分析 ⭐
│       ├── confusion_matrices.png              # 混淆矩阵
│       ├── roc_analysis_fc_data.png           # FC数据ROC曲线
│       ├── roc_analysis_time_series_data.png  # 时间序列ROC曲线
│       └── detailed_analysis_report.md         # 详细分析报告
│
├── 📋 项目报告
│   ├── analysis_summary.md                     # 分析总结报告
│   ├── final_comprehensive_report.md           # 最终综合报告 ⭐
│   └── project_file_index.md                   # 本文件索引
│
└── 💾 原始数据
    ├── data/
    │   ├── HC/                                  # HC组FC矩阵数据
    │   └── MCI/                                 # MCI组FC矩阵数据
    └── node_features/
        ├── HC/                                  # HC组时间序列数据
        └── MCI/                                 # MCI组时间序列数据
```

## 🌟 核心文件说明

### 🎯 最重要的文件（⭐标记）

#### 分析脚本
1. **`time_series_analysis.py`** - 时间序列分析的核心脚本
   - 实现了2101维时域统计特征提取
   - 获得了最佳的组间分离效果（距离6.669）
   - 统计显著性最高（p < 0.001）

2. **`advanced_analysis.py`** - 高级统计和分类分析
   - 包含统计检验、分类性能评估
   - 生成综合可视化和详细报告

3. **`feature_importance_analysis.py`** - 特征重要性和ROC分析
   - 随机森林和排列重要性分析
   - ROC曲线和AUC评估
   - 混淆矩阵生成

#### 结果文件
1. **`results_timeseries/timeseries_tsne_visualization.png`** - 最佳可视化结果
   - 显示HC和MCI组的清晰分离
   - 组间中心距离6.669

2. **`results_advanced/comprehensive_analysis.png`** - 综合对比分析
   - 12个子图全面展示分析结果
   - FC矩阵vs时间序列特征对比

3. **`final_comprehensive_report.md`** - 最终综合报告
   - 完整的项目总结和科学发现
   - 包含所有关键数据和结论

## 📊 数据文件说明

### CSV数据文件
- **`fc_tsne_results.csv`** - FC矩阵t-SNE降维结果
- **`timeseries_tsne_results.csv`** - 时间序列t-SNE降维结果
- 格式：`tSNE1, tSNE2, Group`

### 可视化文件
所有PNG文件均为300 DPI高质量图像，适合学术发表使用。

## 🔬 分析流程

### 1. 基础分析流程
```
原始数据 → 特征提取 → 标准化 → PCA降维 → t-SNE降维 → 可视化
```

### 2. 高级分析流程
```
t-SNE结果 → 统计检验 → 分类性能评估 → 特征重要性分析 → ROC分析
```

## 📈 关键性能指标

| 指标 | FC矩阵 | 时间序列 | 最佳方法 |
|------|--------|----------|----------|
| 组间分离度 | 3.000 | **6.669** | 时间序列 |
| 分类准确率 | 60.5% | **63.3%** | 时间序列 |
| AUC值 | 0.635 | **0.686** | 时间序列 |
| 统计显著性 | 部分显著 | **高度显著** | 时间序列 |

## 🎯 使用建议

### 快速开始
1. 运行 `time_series_analysis.py` 获得最佳分析结果
2. 查看 `results_timeseries/timeseries_tsne_visualization.png`
3. 阅读 `final_comprehensive_report.md` 了解完整结论

### 深入分析
1. 运行 `advanced_analysis.py` 进行统计分析
2. 运行 `feature_importance_analysis.py` 进行特征分析
3. 查看 `results_advanced/` 目录下的所有可视化结果

### 自定义分析
1. 使用 `adaptive_brain_analysis.py` 作为基础框架
2. 根据需要修改特征提取方法
3. 调整可视化参数和分类器设置

## 🔧 技术要求

### Python环境
- Python 3.7+
- NumPy, Pandas, Scikit-learn
- Matplotlib, Seaborn
- SciPy

### 硬件要求
- 内存：至少8GB（推荐16GB）
- 存储：至少2GB可用空间
- CPU：多核处理器（t-SNE计算密集）

## 📝 引用信息

如果使用本项目的方法或结果，请引用：
```
脑功能连接数据分析与可视化项目
基于ADNI数据集的HC vs MCI分析
时间序列时域特征提取和t-SNE降维可视化
2025年
```

## 🆘 故障排除

### 常见问题
1. **内存不足**：减少样本数量或使用增量PCA
2. **t-SNE收敛慢**：调整困惑度参数或增加迭代次数
3. **可视化显示问题**：检查中文字体设置

### 联系支持
如有技术问题，请检查：
1. 数据文件路径是否正确
2. Python包版本是否兼容
3. 内存和存储空间是否充足

---

**项目状态**: ✅ 完成  
**最后更新**: 2025年  
**版本**: v1.0  
**质量等级**: ⭐⭐⭐⭐⭐
