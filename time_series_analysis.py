#!/usr/bin/env python3
"""
时间序列数据分析与可视化
专门分析脑区时间序列的时域统计特征
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import os
import glob
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TimeSeriesAnalyzer:
    def __init__(self, node_features_dir='node_features'):
        """
        初始化时间序列分析器
        
        Parameters:
        -----------
        node_features_dir : str
            时间序列数据目录
        """
        self.node_features_dir = node_features_dir
        self.hc_ts_data = []
        self.mci_ts_data = []
        self.hc_features = []
        self.mci_features = []
        
    def load_time_series_data(self):
        """加载HC和MCI组的时间序列数据"""
        print("正在加载时间序列数据...")
        
        # 加载HC组时间序列
        hc_ts_files = glob.glob(os.path.join(self.node_features_dir, 'HC', 'ROISignals_*.txt'))
        for file in sorted(hc_ts_files):
            try:
                ts_data = np.loadtxt(file)
                # 接受任何有116个脑区的时间序列数据
                if ts_data.shape[1] == 116 and ts_data.shape[0] > 50:  # 至少50个时间点
                    self.hc_ts_data.append(ts_data)
                else:
                    print(f"跳过文件 {file}: 数据尺寸 {ts_data.shape}")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")

        # 加载MCI组时间序列
        mci_ts_files = glob.glob(os.path.join(self.node_features_dir, 'MCI', 'ROISignals_*.txt'))
        for file in sorted(mci_ts_files):
            try:
                ts_data = np.loadtxt(file)
                # 接受任何有116个脑区的时间序列数据
                if ts_data.shape[1] == 116 and ts_data.shape[0] > 50:  # 至少50个时间点
                    self.mci_ts_data.append(ts_data)
                else:
                    print(f"跳过文件 {file}: 数据尺寸 {ts_data.shape}")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        print(f"成功加载 HC组: {len(self.hc_ts_data)} 个时间序列")
        print(f"成功加载 MCI组: {len(self.mci_ts_data)} 个时间序列")
    
    def calculate_comprehensive_temporal_features(self, ts_data):
        """
        计算时间序列的全面时域统计特征
        
        Parameters:
        -----------
        ts_data : numpy.ndarray
            时间序列数据 (time_points, regions)
            
        Returns:
        --------
        features : numpy.ndarray
            时域特征向量
        """
        features = []
        
        # 全局特征（所有脑区的统计）
        global_mean = np.mean(ts_data)
        global_std = np.std(ts_data)
        global_var = np.var(ts_data)
        global_min = np.min(ts_data)
        global_max = np.max(ts_data)
        global_range = global_max - global_min
        
        features.extend([global_mean, global_std, global_var, global_min, global_max, global_range])
        
        # 对每个脑区计算详细统计特征
        region_features = []
        for region in range(ts_data.shape[1]):
            region_ts = ts_data[:, region]
            
            # 基本统计特征
            mean_val = np.mean(region_ts)
            std_val = np.std(region_ts)
            var_val = np.var(region_ts)
            min_val = np.min(region_ts)
            max_val = np.max(region_ts)
            range_val = max_val - min_val
            
            # 高阶统计特征
            skewness = stats.skew(region_ts)
            kurtosis = stats.kurtosis(region_ts)
            
            # 百分位数
            q25 = np.percentile(region_ts, 25)
            q50 = np.percentile(region_ts, 50)  # 中位数
            q75 = np.percentile(region_ts, 75)
            iqr = q75 - q25  # 四分位距
            
            # 能量相关特征
            energy = np.sum(region_ts ** 2)
            rms = np.sqrt(np.mean(region_ts ** 2))
            
            # 变异系数
            cv = std_val / abs(mean_val) if mean_val != 0 else 0
            
            # 零交叉率
            zero_crossings = np.sum(np.diff(np.sign(region_ts - np.mean(region_ts))) != 0)
            
            # 峰值特征
            peaks_above_mean = np.sum(region_ts > (np.mean(region_ts) + np.std(region_ts)))
            peaks_below_mean = np.sum(region_ts < (np.mean(region_ts) - np.std(region_ts)))
            
            region_features.extend([
                mean_val, std_val, var_val, min_val, max_val, range_val,
                skewness, kurtosis, q25, q50, q75, iqr, energy, rms, cv,
                zero_crossings, peaks_above_mean, peaks_below_mean
            ])
        
        # 合并全局特征和区域特征
        all_features = features + region_features
        
        # 计算区域间的相关性特征
        correlation_matrix = np.corrcoef(ts_data.T)
        # 提取上三角矩阵的统计特征
        upper_tri_indices = np.triu_indices(correlation_matrix.shape[0], k=1)
        correlation_values = correlation_matrix[upper_tri_indices]
        
        # 相关性统计特征
        corr_features = [
            np.mean(correlation_values),
            np.std(correlation_values),
            np.min(correlation_values),
            np.max(correlation_values),
            np.median(correlation_values),
            stats.skew(correlation_values),
            stats.kurtosis(correlation_values)
        ]
        
        all_features.extend(corr_features)
        
        return np.array(all_features)
    
    def process_features(self):
        """处理所有被试的时间序列特征"""
        print("正在计算时间序列特征...")
        
        if len(self.hc_ts_data) == 0 or len(self.mci_ts_data) == 0:
            print("错误: 没有足够的时间序列数据进行分析")
            return
        
        # 处理HC组特征
        for i, ts_data in enumerate(self.hc_ts_data):
            features = self.calculate_comprehensive_temporal_features(ts_data)
            # 检查并处理非有限值
            features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
            self.hc_features.append(features)
            if (i + 1) % 50 == 0:
                print(f"已处理 HC组 {i + 1}/{len(self.hc_ts_data)} 个样本")
        
        # 处理MCI组特征
        for i, ts_data in enumerate(self.mci_ts_data):
            features = self.calculate_comprehensive_temporal_features(ts_data)
            # 检查并处理非有限值
            features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
            self.mci_features.append(features)
            if (i + 1) % 50 == 0:
                print(f"已处理 MCI组 {i + 1}/{len(self.mci_ts_data)} 个样本")
        
        print(f"HC组特征维度: {len(self.hc_features)} × {len(self.hc_features[0]) if self.hc_features else 0}")
        print(f"MCI组特征维度: {len(self.mci_features)} × {len(self.mci_features[0]) if self.mci_features else 0}")
    
    def perform_tsne_analysis(self, perplexity=30, n_components=2, random_state=42):
        """执行t-SNE降维分析"""
        print("正在进行t-SNE降维分析...")
        
        if len(self.hc_features) == 0 or len(self.mci_features) == 0:
            print("错误: 没有足够的特征数据进行分析")
            return None, None
        
        # 合并所有特征
        all_features = np.array(self.hc_features + self.mci_features)
        labels = ['HC'] * len(self.hc_features) + ['MCI'] * len(self.mci_features)
        
        print(f"总样本数: {len(all_features)}")
        print(f"特征维度: {all_features.shape[1]}")
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(all_features)
        
        # 先用PCA降维以提高t-SNE效率
        if features_scaled.shape[1] > 50:
            print("使用PCA预处理...")
            pca = PCA(n_components=50, random_state=random_state)
            features_pca = pca.fit_transform(features_scaled)
            print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
        else:
            features_pca = features_scaled
        
        # 调整困惑度参数
        n_samples = len(all_features)
        perplexity = min(perplexity, (n_samples - 1) // 3)
        print(f"使用困惑度: {perplexity}")
        
        # t-SNE降维
        tsne = TSNE(n_components=n_components, perplexity=perplexity, 
                   random_state=random_state, n_iter=1000, verbose=1)
        features_tsne = tsne.fit_transform(features_pca)
        
        return features_tsne, labels
    
    def visualize_results(self, features_tsne, labels, save_path='results_timeseries'):
        """可视化t-SNE结果"""
        print("正在生成可视化结果...")
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'tSNE1': features_tsne[:, 0],
            'tSNE2': features_tsne[:, 1],
            'Group': labels
        })
        
        # 设置图形样式
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 子图1: 基本散点图
        colors = {'HC': '#2E86AB', 'MCI': '#F24236'}
        for group in ['HC', 'MCI']:
            group_data = df[df['Group'] == group]
            axes[0,0].scatter(group_data['tSNE1'], group_data['tSNE2'], 
                          c=colors[group], label=group, alpha=0.7, s=50)
        
        axes[0,0].set_xlabel('t-SNE Component 1', fontsize=12)
        axes[0,0].set_ylabel('t-SNE Component 2', fontsize=12)
        axes[0,0].set_title('HC vs MCI Groups - Time Series Features', fontsize=14)
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 子图2: 密度图
        sns.scatterplot(data=df, x='tSNE1', y='tSNE2', hue='Group', 
                       palette=colors, alpha=0.7, s=50, ax=axes[0,1])
        axes[0,1].set_title('HC vs MCI Groups - Seaborn Style', fontsize=14)
        axes[0,1].grid(True, alpha=0.3)
        
        # 子图3: 分布直方图
        for i, group in enumerate(['HC', 'MCI']):
            group_data = df[df['Group'] == group]
            axes[1,0].hist(group_data['tSNE1'], alpha=0.6, label=f'{group} (tSNE1)', 
                          color=colors[group], bins=20)
        axes[1,0].set_xlabel('t-SNE Component 1')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].set_title('Distribution of t-SNE Component 1')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 子图4: 箱线图
        df_melted = pd.melt(df, id_vars=['Group'], value_vars=['tSNE1', 'tSNE2'], 
                           var_name='Component', value_name='Value')
        sns.boxplot(data=df_melted, x='Component', y='Value', hue='Group', 
                   palette=colors, ax=axes[1,1])
        axes[1,1].set_title('Box Plot of t-SNE Components')
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'timeseries_tsne_visualization.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存数据
        df.to_csv(os.path.join(save_path, 'timeseries_tsne_results.csv'), index=False)
        
        # 计算组间距离统计
        hc_data = df[df['Group'] == 'HC'][['tSNE1', 'tSNE2']].values
        mci_data = df[df['Group'] == 'MCI'][['tSNE1', 'tSNE2']].values
        
        hc_center = np.mean(hc_data, axis=0)
        mci_center = np.mean(mci_data, axis=0)
        center_distance = np.linalg.norm(hc_center - mci_center)
        
        print(f"\n=== 时间序列分析结果统计 ===")
        print(f"HC组样本数: {len(hc_data)}")
        print(f"MCI组样本数: {len(mci_data)}")
        print(f"HC组中心: ({hc_center[0]:.3f}, {hc_center[1]:.3f})")
        print(f"MCI组中心: ({mci_center[0]:.3f}, {mci_center[1]:.3f})")
        print(f"组间中心距离: {center_distance:.3f}")
        print(f"结果已保存到 {save_path} 目录")
        
        return df

def main():
    """主函数"""
    print("=== 脑区时间序列数据分析与可视化 ===")
    
    # 初始化分析器
    analyzer = TimeSeriesAnalyzer()
    
    # 加载数据
    analyzer.load_time_series_data()
    
    # 处理特征
    analyzer.process_features()
    
    # t-SNE分析
    features_tsne, labels = analyzer.perform_tsne_analysis()
    
    if features_tsne is not None:
        # 可视化结果
        results_df = analyzer.visualize_results(features_tsne, labels)
        print("时间序列分析完成！")
    else:
        print("分析失败，请检查数据。")

if __name__ == "__main__":
    main()
